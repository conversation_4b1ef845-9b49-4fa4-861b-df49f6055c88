import * as vscode from 'vscode';
import * as path from 'path';
import { ZephyrProject } from '../types';

export class ZephyrFileWatcher {
    private watchers: vscode.FileSystemWatcher[] = [];
    private onProjectChangedCallback?: (project: ZephyrProject) => void;

    constructor() {
        this.setupWatchers();
    }

    private setupWatchers() {
        // Watch for CMakeLists.txt changes
        const cmakeWatcher = vscode.workspace.createFileSystemWatcher('**/CMakeLists.txt');
        cmakeWatcher.onDidChange(this.handleFileChange.bind(this));
        cmakeWatcher.onDidCreate(this.handleFileChange.bind(this));
        cmakeWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(cmakeWatcher);

        // Watch for prj.conf changes
        const prjConfWatcher = vscode.workspace.createFileSystemWatcher('**/prj.conf');
        prjConfWatcher.onDidChange(this.handleFileChange.bind(this));
        prjConfWatcher.onDidCreate(this.handleFileChange.bind(this));
        prjConfWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(prjConfWatcher);

        // Watch for device tree files
        const dtsiWatcher = vscode.workspace.createFileSystemWatcher('**/*.dtsi');
        dtsiWatcher.onDidChange(this.handleFileChange.bind(this));
        dtsiWatcher.onDidCreate(this.handleFileChange.bind(this));
        dtsiWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(dtsiWatcher);

        // Watch for overlay files
        const overlayWatcher = vscode.workspace.createFileSystemWatcher('**/*.overlay');
        overlayWatcher.onDidChange(this.handleFileChange.bind(this));
        overlayWatcher.onDidCreate(this.handleFileChange.bind(this));
        overlayWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(overlayWatcher);

        // Watch for YAML files
        const yamlWatcher = vscode.workspace.createFileSystemWatcher('**/*.{yaml,yml}');
        yamlWatcher.onDidChange(this.handleFileChange.bind(this));
        yamlWatcher.onDidCreate(this.handleFileChange.bind(this));
        yamlWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(yamlWatcher);

        // Watch for Kconfig files
        const kconfigWatcher = vscode.workspace.createFileSystemWatcher('**/Kconfig*');
        kconfigWatcher.onDidChange(this.handleFileChange.bind(this));
        kconfigWatcher.onDidCreate(this.handleFileChange.bind(this));
        kconfigWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(kconfigWatcher);
    }

    private async handleFileChange(uri: vscode.Uri) {
        console.log(`File changed: ${uri.fsPath}`);
        
        if (this.onProjectChangedCallback) {
            // Determine which project this file belongs to
            const projectPath = this.findProjectRoot(uri.fsPath);
            if (projectPath) {
                // Create a minimal project object for the callback
                const project: ZephyrProject = {
                    name: path.basename(projectPath),
                    path: projectPath,
                    boardConfig: 'unknown',
                    dtsiFiles: [],
                    yamlFiles: [],
                    kconfigFiles: []
                };
                
                this.onProjectChangedCallback(project);
            }
        }
    }

    private findProjectRoot(filePath: string): string | null {
        let currentDir = path.dirname(filePath);
        
        while (currentDir !== path.dirname(currentDir)) {
            // Check if this directory contains Zephyr project indicators
            const cmakeFile = path.join(currentDir, 'CMakeLists.txt');
            const prjConfFile = path.join(currentDir, 'prj.conf');
            
            if (require('fs').existsSync(cmakeFile) || require('fs').existsSync(prjConfFile)) {
                return currentDir;
            }
            
            currentDir = path.dirname(currentDir);
        }
        
        return null;
    }

    onProjectChanged(callback: (project: ZephyrProject) => void) {
        this.onProjectChangedCallback = callback;
    }

    dispose() {
        this.watchers.forEach(watcher => watcher.dispose());
        this.watchers = [];
    }
}
