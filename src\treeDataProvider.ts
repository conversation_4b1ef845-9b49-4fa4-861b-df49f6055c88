import * as vscode from 'vscode';
import * as path from 'path';
import { ZephyrProject } from './types';
import { ZephyrProjectScanner } from './utils/projectScanner';

export class ZephyrProjectProvider implements vscode.TreeDataProvider<ZephyrProjectItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<ZephyrProjectItem | undefined | null | void> = new vscode.EventEmitter<ZephyrProjectItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<ZephyrProjectItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private projects: ZephyrProject[] = [];

    constructor(private projectScanner: ZephyrProjectScanner) {}

    async refresh(): Promise<void> {
        const scanResult = await this.projectScanner.scanWorkspace();
        this.projects = scanResult.projects;
        
        if (scanResult.errors.length > 0) {
            vscode.window.showWarningMessage(
                `Zephyr project scan completed with errors: ${scanResult.errors.join(', ')}`
            );
        }

        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: ZephyrProjectItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: ZephyrProjectItem): Thenable<ZephyrProjectItem[]> {
        if (!element) {
            // Root level - return projects
            return Promise.resolve(this.projects.map(project => new ZephyrProjectItem(
                project.name,
                project.path,
                project.boardConfig,
                vscode.TreeItemCollapsibleState.Collapsed,
                project
            )));
        } else {
            // Project level - return project details
            return Promise.resolve(this.getProjectChildren(element.project));
        }
    }

    private getProjectChildren(project: ZephyrProject): ZephyrProjectItem[] {
        const children: ZephyrProjectItem[] = [];

        // Board configuration
        children.push(new ZephyrProjectItem(
            `Board: ${project.boardConfig}`,
            '',
            '',
            vscode.TreeItemCollapsibleState.None,
            project,
            'board'
        ));

        // Device tree files
        if (project.dtsiFiles.length > 0) {
            const dtsiItem = new ZephyrProjectItem(
                `Device Tree Files (${project.dtsiFiles.length})`,
                '',
                '',
                vscode.TreeItemCollapsibleState.Collapsed,
                project,
                'dtsi-folder'
            );
            children.push(dtsiItem);
        }

        // YAML files
        if (project.yamlFiles.length > 0) {
            const yamlItem = new ZephyrProjectItem(
                `YAML Files (${project.yamlFiles.length})`,
                '',
                '',
                vscode.TreeItemCollapsibleState.Collapsed,
                project,
                'yaml-folder'
            );
            children.push(yamlItem);
        }

        // Kconfig files
        if (project.kconfigFiles.length > 0) {
            const kconfigItem = new ZephyrProjectItem(
                `Kconfig Files (${project.kconfigFiles.length})`,
                '',
                '',
                vscode.TreeItemCollapsibleState.Collapsed,
                project,
                'kconfig-folder'
            );
            children.push(kconfigItem);
        }

        return children;
    }

    getProjects(): ZephyrProject[] {
        return this.projects;
    }

    getProject(projectPath: string): ZephyrProject | undefined {
        return this.projects.find(p => p.path === projectPath);
    }
}

export class ZephyrProjectItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly projectPath: string,
        public readonly boardConfig: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly project: ZephyrProject,
        public readonly itemType: string = 'project'
    ) {
        super(label, collapsibleState);

        this.tooltip = this.getTooltip();
        this.description = this.getDescription();
        this.contextValue = this.getContextValue();
        this.iconPath = this.getIconPath();

        if (itemType === 'project') {
            this.command = {
                command: 'zephyr.selectProject',
                title: 'Select Project',
                arguments: [project]
            };
        }
    }

    private getTooltip(): string {
        switch (this.itemType) {
            case 'project':
                return `Zephyr Project: ${this.project.name}\nPath: ${this.project.path}\nBoard: ${this.project.boardConfig}`;
            case 'board':
                return `Board Configuration: ${this.project.boardConfig}`;
            case 'dtsi-folder':
                return `Device Tree Source Files (${this.project.dtsiFiles.length} files)`;
            case 'yaml-folder':
                return `YAML Configuration Files (${this.project.yamlFiles.length} files)`;
            case 'kconfig-folder':
                return `Kconfig Files (${this.project.kconfigFiles.length} files)`;
            default:
                return this.label;
        }
    }

    private getDescription(): string {
        switch (this.itemType) {
            case 'project':
                return this.project.boardConfig;
            default:
                return '';
        }
    }

    private getContextValue(): string {
        switch (this.itemType) {
            case 'project':
                return 'zephyrProject';
            case 'board':
                return 'zephyrBoard';
            case 'dtsi-folder':
                return 'zephyrDtsiFolder';
            case 'yaml-folder':
                return 'zephyrYamlFolder';
            case 'kconfig-folder':
                return 'zephyrKconfigFolder';
            default:
                return 'zephyrItem';
        }
    }

    private getIconPath(): vscode.ThemeIcon {
        switch (this.itemType) {
            case 'project':
                return new vscode.ThemeIcon('folder-opened');
            case 'board':
                return new vscode.ThemeIcon('circuit-board');
            case 'dtsi-folder':
                return new vscode.ThemeIcon('file-code');
            case 'yaml-folder':
                return new vscode.ThemeIcon('file-text');
            case 'kconfig-folder':
                return new vscode.ThemeIcon('settings-gear');
            default:
                return new vscode.ThemeIcon('file');
        }
    }
}
