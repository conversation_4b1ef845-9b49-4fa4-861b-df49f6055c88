import * as vscode from 'vscode';
import * as path from 'path';
import * as cp from 'child_process';
import axios from 'axios';
import { PythonServerConfig, ApiResponse } from './types';

export class PythonServerManager {
    private serverProcess: cp.ChildProcess | null = null;
    private config: PythonServerConfig;
    private isStarting = false;
    private isRunning = false;

    constructor(private context: vscode.ExtensionContext) {
        this.config = {
            host: '127.0.0.1',
            port: 8000,
            autoStart: true
        };
    }

    async start(): Promise<boolean> {
        if (this.isRunning || this.isStarting) {
            return true;
        }

        this.isStarting = true;

        try {
            // Check if server is already running
            if (await this.isServerRunning()) {
                this.isRunning = true;
                this.isStarting = false;
                return true;
            }

            // Start the Python server
            const pythonPath = await this.findPythonExecutable();
            const serverScript = path.join(this.context.extensionPath, 'python-backend', 'main.py');

            console.log(`Starting Python server: ${pythonPath} ${serverScript}`);

            this.serverProcess = cp.spawn(pythonPath, [serverScript], {
                cwd: path.join(this.context.extensionPath, 'python-backend'),
                env: {
                    ...process.env,
                    ZEPHYR_BACKEND_HOST: this.config.host,
                    ZEPHYR_BACKEND_PORT: this.config.port.toString(),
                    PYTHONPATH: path.join(this.context.extensionPath, 'python-backend')
                }
            });

            this.serverProcess.stdout?.on('data', (data) => {
                console.log(`Python server stdout: ${data}`);
            });

            this.serverProcess.stderr?.on('data', (data) => {
                console.error(`Python server stderr: ${data}`);
            });

            this.serverProcess.on('close', (code) => {
                console.log(`Python server exited with code ${code}`);
                this.isRunning = false;
                this.serverProcess = null;
            });

            this.serverProcess.on('error', (error) => {
                console.error(`Python server error: ${error}`);
                this.isRunning = false;
                this.serverProcess = null;
                vscode.window.showErrorMessage(`Failed to start Python backend: ${error.message}`);
            });

            // Wait for server to be ready
            const maxRetries = 30;
            let retries = 0;
            
            while (retries < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (await this.isServerRunning()) {
                    this.isRunning = true;
                    this.isStarting = false;
                    console.log('Python server is ready');
                    return true;
                }
                
                retries++;
            }

            throw new Error('Server failed to start within timeout period');

        } catch (error) {
            this.isStarting = false;
            console.error('Failed to start Python server:', error);
            vscode.window.showErrorMessage(`Failed to start Python backend: ${error}`);
            return false;
        }
    }

    async stop(): Promise<void> {
        if (this.serverProcess) {
            this.serverProcess.kill();
            this.serverProcess = null;
        }
        this.isRunning = false;
    }

    private async findPythonExecutable(): Promise<string> {
        // Try to find Python executable
        const pythonCommands = ['python3', 'python', 'py'];
        
        for (const cmd of pythonCommands) {
            try {
                const result = cp.execSync(`${cmd} --version`, { encoding: 'utf8' });
                if (result.includes('Python 3.')) {
                    return cmd;
                }
            } catch (error) {
                // Continue to next command
            }
        }

        // Check if Python extension is available
        const pythonExtension = vscode.extensions.getExtension('ms-python.python');
        if (pythonExtension) {
            try {
                const pythonPath = await vscode.commands.executeCommand('python.interpreterPath');
                if (pythonPath && typeof pythonPath === 'string') {
                    return pythonPath;
                }
            } catch (error) {
                console.warn('Could not get Python path from Python extension:', error);
            }
        }

        throw new Error('Python 3 executable not found. Please install Python 3 or ensure it is in your PATH.');
    }

    private async isServerRunning(): Promise<boolean> {
        try {
            const response = await axios.get(`http://${this.config.host}:${this.config.port}/health`, {
                timeout: 2000
            });
            return response.status === 200;
        } catch (error) {
            return false;
        }
    }

    async makeRequest<T>(endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data?: any): Promise<ApiResponse<T>> {
        if (!this.isRunning) {
            throw new Error('Python server is not running');
        }

        try {
            const url = `http://${this.config.host}:${this.config.port}/api${endpoint}`;
            const config = {
                method,
                url,
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                },
                ...(data && { data })
            };

            const response = await axios(config);
            
            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error(`API request failed: ${error}`);
            
            if (axios.isAxiosError(error)) {
                return {
                    success: false,
                    error: error.response?.data?.detail || error.message
                };
            }
            
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    getServerUrl(): string {
        return `http://${this.config.host}:${this.config.port}`;
    }

    isServerRunning(): boolean {
        return this.isRunning;
    }
}
