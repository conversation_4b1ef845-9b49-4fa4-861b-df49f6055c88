export interface ZephyrProject {
    name: string;
    path: string;
    boardConfig: string;
    dtsiFiles: string[];
    yamlFiles: string[];
    cmakeFile?: string;
    prjConfFile?: string;
    kconfigFiles: string[];
}

export interface ClockConfig {
    id?: number;
    projectId: string;
    configName: string;
    enabled: boolean;
    source: string;
    frequency: number;
    unit: 'Hz' | 'KHz' | 'MHz';
    description?: string;
}

export interface ClockConfigState {
    selectedProject: string | null;
    clockEnabled: boolean;
    clockSource: string;
    frequency: number;
    unit: 'Hz' | 'KHz' | 'MHz';
    isLoading: boolean;
    error: string | null;
    availableSources: string[];
}

export interface ProjectScanResult {
    projects: ZephyrProject[];
    errors: string[];
}

export interface WebviewMessage {
    type: string;
    payload?: any;
}

export interface PythonServerConfig {
    host: string;
    port: number;
    autoStart: boolean;
}

export interface FileParseResult {
    clockConfigs: ClockConfig[];
    deviceTreeData: any;
    kconfigData: any;
    errors: string[];
}

export interface DatabaseProject {
    id: number;
    name: string;
    path: string;
    lastUpdated: Date;
}

export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
}
