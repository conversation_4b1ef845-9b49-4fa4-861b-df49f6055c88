{"name": "zephyr-clock-configurator", "displayName": "Zephyr Clock Configurator", "description": "A comprehensive clock configuration tool for Zephyr RTOS projects", "version": "0.1.0", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["workspaceContains:**/CMakeLists.txt", "workspaceContains:**/prj.conf"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "zephyr-clock-config", "title": "Zephyr Clock Config", "icon": "$(clock)"}]}, "views": {"zephyr-clock-config": [{"id": "zephyr-projects", "name": "Zephyr Projects", "when": "workbenchState != empty"}]}, "commands": [{"command": "zephyr.refreshProjects", "title": "Refresh Projects", "icon": "$(refresh)"}, {"command": "zephyr.launchConfigurator", "title": "Launch Configurator", "icon": "$(gear)"}, {"command": "zephyr.selectProject", "title": "Select Project"}], "menus": {"view/title": [{"command": "zephyr.refreshProjects", "when": "view == zephyr-projects", "group": "navigation"}], "view/item/context": [{"command": "zephyr.launchConfigurator", "when": "view == zephyr-projects && viewItem == zephyrProject", "group": "inline"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "build-webview": "cd webview-ui && npm run build", "dev-webview": "cd webview-ui && npm run dev", "setup-python": "cd python-backend && pip install -r requirements.txt"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0"}}