#!/usr/bin/env python3
"""
Zephyr Clock Configurator Backend Server
Main FastAPI application entry point
"""

import asyncio
import logging
import os
import sys
from contextlib import asynccontextmanager
from pathlib import Path

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

from app.api.routes import api_router
from app.core.config import settings
from app.core.database import init_db
from app.services.file_watcher import FileWatcherService
from app.services.project_manager import ProjectManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global services
file_watcher_service: FileWatcherService = None
project_manager: ProjectManager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global file_watcher_service, project_manager
    
    logger.info("Starting Zephyr Clock Configurator Backend...")
    
    # Initialize database
    await init_db()
    
    # Initialize services
    project_manager = ProjectManager()
    file_watcher_service = FileWatcherService(project_manager)
    
    # Start file watcher
    await file_watcher_service.start()
    
    logger.info("Backend services started successfully")
    
    yield
    
    # Cleanup
    if file_watcher_service:
        await file_watcher_service.stop()
    
    logger.info("Backend services stopped")


def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title="Zephyr Clock Configurator API",
        description="Backend API for Zephyr RTOS Clock Configuration",
        version="0.1.0",
        lifespan=lifespan
    )
    
    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # In production, specify exact origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include API routes
    app.include_router(api_router, prefix="/api")
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "version": "0.1.0"}
    
    return app


app = create_app()


def main():
    """Main entry point for the application"""
    try:
        # Check if running as standalone or from VSCode extension
        port = int(os.getenv("ZEPHYR_BACKEND_PORT", "8000"))
        host = os.getenv("ZEPHYR_BACKEND_HOST", "127.0.0.1")
        
        logger.info(f"Starting server on {host}:{port}")
        
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=False,  # Disable reload in production
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
